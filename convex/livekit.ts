"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { AccessToken } from "livekit-server-sdk";

export const createToken = action({
  args: {
    roomName: v.string(),
    participantName: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get room info
    const room = await ctx.runQuery(api.rooms.getRoom, { name: args.roomName });
    if (!room) {
      throw new Error("Room not found");
    }

    // Check if user is banned
    const user = await ctx.runQuery(api.users.getUser, { userId: identity.subject });
    if (user?.isBanned) {
      throw new Error("User is banned");
    }

    // For Meet-style rooms, everyone can publish and subscribe by default
    // Only the host gets admin privileges
    const isHost = room.hostId === identity.subject;
    
    const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
      identity: identity.subject,
      name: args.participantName,
    });

    at.addGrant({
      roomJoin: true,
      room: args.roomName,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      roomAdmin: isHost,
    });

    return await at.toJwt();
  },
});