import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const create = mutation({
  args: {
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    // Check if room with this name already exists
    const existingRoom = await ctx.db
      .query("rooms")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    if (existingRoom) {
      return existingRoom._id;
    }

    // Create new room
    const roomId = await ctx.db.insert("rooms", {
      name: args.name,
      title: args.title,
      description: args.description,
      hostId: identity.subject,
      isActive: true,
      maxParticipants: 100,
      settings: {
        allowScreenShare: true,
        allowChat: true,
        requireAuth: false,
      },
    });

    return roomId;
  },
});

export const getRoom = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    const room = await ctx.db
      .query("rooms")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    return room;
  },
});

export const getMyRooms = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .order("desc")
      .take(10);

    return rooms;
  },
});

export const updateRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    if (room.hostId !== identity.subject) {
      throw new Error("Not authorized to update this room");
    }

    const updateData: any = {};
    if (args.title !== undefined) updateData.title = args.title;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;
    if (args.settings !== undefined) updateData.settings = args.settings;

    await ctx.db.patch(args.roomId, updateData);
    return room;
  },
});

export const deleteRoom = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    if (room.hostId !== identity.subject) {
      throw new Error("Not authorized to delete this room");
    }

    await ctx.db.delete(args.roomId);
    return { success: true };
  },
});

export const getRoomAnalytics = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return {
        totalRooms: 0,
        activeRooms: 0,
        totalParticipants: 0,
        growth: "0%",
      };
    }

    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_host", (q) => q.eq("hostId", identity.subject))
      .collect();

    const activeRooms = rooms.filter(room => room.isActive).length;

    return {
      totalRooms: rooms.length,
      activeRooms,
      totalParticipants: 0, // This would need to be calculated from participant data
      growth: "0%", // This would need historical data
    };
  },
});