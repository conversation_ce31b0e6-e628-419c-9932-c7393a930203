"use client";

import React, { useState } from 'react';
import {
  LiveKitRoom,
  GridLayout,
  ParticipantTile,
  ControlBar,
  ChatEntry,
  Chat,
  RoomAudioRenderer,
  useParticipants,
  useTracks,
} from '@livekit/components-react';
import { Track } from 'livekit-client';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  PhoneOff, 
  MessageSquare,
  Users,
  Monitor,
  MoreVertical
} from 'lucide-react';

interface VideoConferenceProps {
  roomName: string;
  token: string;
  userName: string;
}

function MeetingRoom() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isParticipantsOpen, setIsParticipantsOpen] = useState(false);
  const participants = useParticipants();
  
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  return (
    <div className="h-screen bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 text-white px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h1 className="font-medium">Meeting Room</h1>
          <div className="flex items-center space-x-2 text-sm text-gray-300">
            <Users className="w-4 h-4" />
            <span>{participants.length} participants</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsParticipantsOpen(!isParticipantsOpen)}
            className={`p-2 rounded-lg transition-colors ${
              isParticipantsOpen ? 'bg-blue-600' : 'hover:bg-gray-700'
            }`}
          >
            <Users className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => setIsChatOpen(!isChatOpen)}
            className={`p-2 rounded-lg transition-colors ${
              isChatOpen ? 'bg-blue-600' : 'hover:bg-gray-700'
            }`}
          >
            <MessageSquare className="w-5 h-5" />
          </button>
          
          <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex relative overflow-hidden">
        {/* Video Grid */}
        <div className="flex-1 p-4">
          <GridLayout tracks={tracks} style={{ height: '100%' }}>
            <ParticipantTile />
          </GridLayout>
        </div>

        {/* Side Panel */}
        {(isChatOpen || isParticipantsOpen) && (
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            {/* Panel Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex space-x-4">
                <button
                  onClick={() => {
                    setIsParticipantsOpen(true);
                    setIsChatOpen(false);
                  }}
                  className={`pb-2 border-b-2 transition-colors ${
                    isParticipantsOpen
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Participants ({participants.length})
                </button>
                <button
                  onClick={() => {
                    setIsChatOpen(true);
                    setIsParticipantsOpen(false);
                  }}
                  className={`pb-2 border-b-2 transition-colors ${
                    isChatOpen
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Chat
                </button>
              </div>
            </div>

            {/* Panel Content */}
            <div className="flex-1 overflow-hidden">
              {isParticipantsOpen && (
                <div className="p-4 space-y-3">
                  {participants.map((participant) => (
                    <div key={participant.identity} className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {participant.name?.[0]?.toUpperCase() || 'A'}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          {participant.name || 'Anonymous'}
                          {participant.isLocal && ' (You)'}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          {participant.isMicrophoneEnabled ? (
                            <Mic className="w-3 h-3 text-green-500" />
                          ) : (
                            <MicOff className="w-3 h-3 text-red-500" />
                          )}
                          {participant.isCameraEnabled ? (
                            <Video className="w-3 h-3 text-green-500" />
                          ) : (
                            <VideoOff className="w-3 h-3 text-red-500" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {isChatOpen && (
                <div className="h-full">
                  <Chat style={{ height: '100%' }} />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-gray-800 p-4">
        <div className="flex items-center justify-center">
          <ControlBar variation="minimal" />
        </div>
      </div>

      {/* Audio Renderer */}
      <RoomAudioRenderer />
    </div>
  );
}

export function VideoConference({ roomName, token, userName }: VideoConferenceProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_WS_URL || 'ws://localhost:7880';

  return (
    <LiveKitRoom
      video={true}
      audio={true}
      token={token}
      serverUrl={serverUrl}
      data-lk-theme="default"
      style={{ height: '100vh' }}
    >
      <MeetingRoom />
    </LiveKitRoom>
  );
}