/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/rooms/[roomName]/page";
exports.ids = ["app/rooms/[roomName]/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f126a7a969b1105c221f5710d89250ac20d205bab%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fb39e1ae80adaf031bbbe57e170bd653b7110b56a%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc968e88d4d7d85319b0c692c8eba3563d91942c2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227f2049dbe655e5196d542e38101773d6697a94154a%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f126a7a969b1105c221f5710d89250ac20d205bab%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fb39e1ae80adaf031bbbe57e170bd653b7110b56a%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc968e88d4d7d85319b0c692c8eba3563d91942c2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227f2049dbe655e5196d542e38101773d6697a94154a%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7f126a7a969b1105c221f5710d89250ac20d205bab\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.deleteKeylessAction),\n/* harmony export */   \"7f2049dbe655e5196d542e38101773d6697a94154a\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__.invalidateCacheAction),\n/* harmony export */   \"7fb39e1ae80adaf031bbbe57e170bd653b7110b56a\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.createOrReadKeylessAction),\n/* harmony export */   \"7fc968e88d4d7d85319b0c692c8eba3563d91942c2\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.syncKeylessConfigAction)\n/* harmony export */ });\n/* harmony import */ var _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n/* harmony import */ var _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f126a7a969b1105c221f5710d89250ac20d205bab%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227fb39e1ae80adaf031bbbe57e170bd653b7110b56a%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227fc968e88d4d7d85319b0c692c8eba3563d91942c2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227f2049dbe655e5196d542e38101773d6697a94154a%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"00d9356e193f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDBkOTM1NmUxOTNmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_convex_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/convex-provider */ \"(rsc)/./components/convex-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_livepeer_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/livepeer-provider */ \"(rsc)/./components/livepeer-provider.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Meet Clone - Video meetings for everyone\",\n    description: \"Connect, collaborate and celebrate from anywhere with secure, high-quality video meetings.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_convex_provider__WEBPACK_IMPORTED_MODULE_2__.ConvexClientProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_livepeer_provider__WEBPACK_IMPORTED_MODULE_4__.LivepeerProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"dark\",\n                        enableSystem: false,\n                        storageKey: \"streamyard-clone-theme\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                                theme: \"light\",\n                                position: \"bottom-center\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/rooms/[roomName]/page.tsx":
/*!***************************************!*\
  !*** ./app/rooms/[roomName]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/convex-provider.tsx":
/*!****************************************!*\
  !*** ./components/convex-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConvexClientProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/convex-provider.tsx",
"ConvexClientProvider",
);

/***/ }),

/***/ "(rsc)/./components/livepeer-provider.tsx":
/*!******************************************!*\
  !*** ./components/livepeer-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LivepeerProvider: () => (/* binding */ LivepeerProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LivepeerProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx",
"LivepeerProvider",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frooms%2F%5BroomName%5D%2Fpage&page=%2Frooms%2F%5BroomName%5D%2Fpage&appPaths=%2Frooms%2F%5BroomName%5D%2Fpage&pagePath=private-next-app-dir%2Frooms%2F%5BroomName%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frooms%2F%5BroomName%5D%2Fpage&page=%2Frooms%2F%5BroomName%5D%2Fpage&appPaths=%2Frooms%2F%5BroomName%5D%2Fpage&pagePath=private-next-app-dir%2Frooms%2F%5BroomName%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/rooms/[roomName]/page.tsx */ \"(rsc)/./app/rooms/[roomName]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'rooms',\n        {\n        children: [\n        '[roomName]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/streamyard-clonez/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/rooms/[roomName]/page\",\n        pathname: \"/rooms/[roomName]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frooms%2F%5BroomName%5D%2Fpage&page=%2Frooms%2F%5BroomName%5D%2Fpage&appPaths=%2Frooms%2F%5BroomName%5D%2Fpage&pagePath=private-next-app-dir%2Frooms%2F%5BroomName%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/convex-provider.tsx */ \"(rsc)/./components/convex-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/livepeer-provider.tsx */ \"(rsc)/./components/livepeer-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/rooms/[roomName]/page.tsx */ \"(rsc)/./app/rooms/[roomName]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZhcHAlMkZyb29tcyUyRiU1QnJvb21OYW1lJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9hcHAvcm9vbXMvW3Jvb21OYW1lXS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/rooms/[roomName]/page.tsx":
/*!***************************************!*\
  !*** ./app/rooms/[roomName]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoomPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../convex/_generated/api */ \"(ssr)/./convex/_generated/api.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var _components_meet_VideoConference__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/meet/VideoConference */ \"(ssr)/./components/meet/VideoConference.tsx\");\n/* harmony import */ var _components_meet_PreJoinScreen__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/meet/PreJoinScreen */ \"(ssr)/./components/meet/PreJoinScreen.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction RoomPage() {\n    const { roomName } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_7__.useUser)();\n    const [hasJoined, setHasJoined] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('');\n    const room = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.rooms.getRoom, {\n        name: roomName\n    });\n    const createRoom = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.rooms.create);\n    const createToken = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.livekit.createToken);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"RoomPage.useEffect\": ()=>{\n            if (user) {\n                setUserName(user.firstName || user.username || 'Anonymous');\n            }\n        }\n    }[\"RoomPage.useEffect\"], [\n        user\n    ]);\n    const handleJoinRoom = async (displayName)=>{\n        try {\n            // Create room if it doesn't exist\n            if (!room) {\n                await createRoom({\n                    name: roomName,\n                    title: `Meeting: ${roomName}`,\n                    description: `Video meeting room for ${roomName}`\n                });\n            }\n            // Generate LiveKit token\n            const liveKitToken = await createToken({\n                roomName: roomName,\n                participantName: displayName || userName || 'Anonymous'\n            });\n            setToken(liveKitToken);\n            setHasJoined(true);\n        } catch (error) {\n            console.error('Failed to join room:', error);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Please sign in to join the meeting\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/sign-in\",\n                        className: \"px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\",\n                        children: \"Sign In\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasJoined || !token) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_meet_PreJoinScreen__WEBPACK_IMPORTED_MODULE_5__.PreJoinScreen, {\n            roomName: roomName,\n            userName: userName,\n            onJoin: handleJoinRoom\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_meet_VideoConference__WEBPACK_IMPORTED_MODULE_4__.VideoConference, {\n        roomName: roomName,\n        token: token,\n        userName: userName\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/app/rooms/[roomName]/page.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/rooms/[roomName]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/convex-provider.tsx":
/*!****************************************!*\
  !*** ./components/convex-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var convex_react_clerk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react-clerk */ \"(ssr)/./node_modules/convex/dist/esm/react-clerk/index.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\");\n/* __next_internal_client_entry_do_not_use__ ConvexClientProvider auto */ \n\n\n\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(\"https://wonderful-kangaroo-238.convex.cloud\");\nfunction ConvexClientProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.ClerkProvider, {\n        publishableKey: \"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(convex_react_clerk__WEBPACK_IMPORTED_MODULE_2__.ConvexProviderWithClerk, {\n            client: convex,\n            useAuth: _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.usePromisifiedAuth,\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/convex-provider.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/convex-provider.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2NvbnZleC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUQ7QUFDWTtBQUNOO0FBRXZELE1BQU1JLFNBQVMsSUFBSUosMkRBQWlCQSxDQUFDSyw2Q0FBa0M7QUFFaEUsU0FBU0cscUJBQXFCLEVBQ25DQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ1Asd0RBQWFBO1FBQUNRLGdCQUFnQkwseURBQTZDO2tCQUMxRSw0RUFBQ0osdUVBQXVCQTtZQUFDVyxRQUFRUjtZQUFRRCxTQUFTQSw2REFBT0E7c0JBQ3RETTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9jb21wb25lbnRzL2NvbnZleC1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IENvbnZleFJlYWN0Q2xpZW50IH0gZnJvbSBcImNvbnZleC9yZWFjdFwiO1xuaW1wb3J0IHsgQ29udmV4UHJvdmlkZXJXaXRoQ2xlcmsgfSBmcm9tIFwiY29udmV4L3JlYWN0LWNsZXJrXCI7XG5pbXBvcnQgeyBDbGVya1Byb3ZpZGVyLCB1c2VBdXRoIH0gZnJvbSBcIkBjbGVyay9uZXh0anNcIjtcblxuY29uc3QgY29udmV4ID0gbmV3IENvbnZleFJlYWN0Q2xpZW50KHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTlZFWF9VUkwhKTtcblxuZXhwb3J0IGZ1bmN0aW9uIENvbnZleENsaWVudFByb3ZpZGVyKHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPENsZXJrUHJvdmlkZXIgcHVibGlzaGFibGVLZXk9e3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMRVJLX1BVQkxJU0hBQkxFX0tFWSF9PlxuICAgICAgPENvbnZleFByb3ZpZGVyV2l0aENsZXJrIGNsaWVudD17Y29udmV4fSB1c2VBdXRoPXt1c2VBdXRofT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Db252ZXhQcm92aWRlcldpdGhDbGVyaz5cbiAgICA8L0NsZXJrUHJvdmlkZXI+XG4gICk7XG59Il0sIm5hbWVzIjpbIkNvbnZleFJlYWN0Q2xpZW50IiwiQ29udmV4UHJvdmlkZXJXaXRoQ2xlcmsiLCJDbGVya1Byb3ZpZGVyIiwidXNlQXV0aCIsImNvbnZleCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19DT05WRVhfVVJMIiwiQ29udmV4Q2xpZW50UHJvdmlkZXIiLCJjaGlsZHJlbiIsInB1Ymxpc2hhYmxlS2V5IiwiTkVYVF9QVUJMSUNfQ0xFUktfUFVCTElTSEFCTEVfS0VZIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/convex-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/livepeer-provider.tsx":
/*!******************************************!*\
  !*** ./components/livepeer-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LivepeerProvider: () => (/* binding */ LivepeerProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ LivepeerProvider auto */ \nfunction LivepeerProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xpdmVwZWVyLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRU8sU0FBU0EsaUJBQWlCLEVBQy9CQyxRQUFRLEVBR1Q7SUFDQyxxQkFBTztrQkFBR0E7O0FBQ1oiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovY29tcG9uZW50cy9saXZlcGVlci1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBMaXZlcGVlclByb3ZpZGVyKHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG59Il0sIm5hbWVzIjpbIkxpdmVwZWVyUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/livepeer-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/meet/PreJoinScreen.tsx":
/*!*******************************************!*\
  !*** ./components/meet/PreJoinScreen.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreJoinScreen: () => (/* binding */ PreJoinScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Settings,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Settings,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Settings,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Settings,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Settings,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ PreJoinScreen auto */ \n\n\nfunction PreJoinScreen({ roomName, userName, onJoin }) {\n    const [displayName, setDisplayName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(userName);\n    const [isVideoEnabled, setIsVideoEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAudioEnabled, setIsAudioEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [stream, setStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreJoinScreen.useEffect\": ()=>{\n            if (isVideoEnabled) {\n                startPreview();\n            } else {\n                stopPreview();\n            }\n            return ({\n                \"PreJoinScreen.useEffect\": ()=>{\n                    stopPreview();\n                }\n            })[\"PreJoinScreen.useEffect\"];\n        }\n    }[\"PreJoinScreen.useEffect\"], [\n        isVideoEnabled\n    ]);\n    const startPreview = async ()=>{\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                video: isVideoEnabled,\n                audio: isAudioEnabled\n            });\n            setStream(mediaStream);\n            if (videoRef.current) {\n                videoRef.current.srcObject = mediaStream;\n            }\n        } catch (error) {\n            console.error('Error accessing media devices:', error);\n        }\n    };\n    const stopPreview = ()=>{\n        if (stream) {\n            stream.getTracks().forEach((track)=>track.stop());\n            setStream(null);\n        }\n    };\n    const handleJoin = ()=>{\n        stopPreview();\n        onJoin(displayName || userName || 'Anonymous');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-md w-full p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Ready to join?\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Meeting: \",\n                                roomName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative bg-gray-900 rounded-lg overflow-hidden mb-6 aspect-video\",\n                    children: [\n                        isVideoEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                            ref: videoRef,\n                            autoPlay: true,\n                            muted: true,\n                            playsInline: true,\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xl font-semibold\",\n                                    children: (displayName || userName || 'A')[0].toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsVideoEnabled(!isVideoEnabled),\n                                    className: `p-3 rounded-full transition-colors ${isVideoEnabled ? 'bg-gray-600 hover:bg-gray-700' : 'bg-red-600 hover:bg-red-700'}`,\n                                    children: isVideoEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsAudioEnabled(!isAudioEnabled),\n                                    className: `p-3 rounded-full transition-colors ${isAudioEnabled ? 'bg-gray-600 hover:bg-gray-700' : 'bg-red-600 hover:bg-red-700'}`,\n                                    children: isAudioEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"displayName\",\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"Your name\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            id: \"displayName\",\n                            type: \"text\",\n                            value: displayName,\n                            onChange: (e)=>setDisplayName(e.target.value),\n                            placeholder: \"Enter your name\",\n                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleJoin,\n                    disabled: !displayName.trim(),\n                    className: \"w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: \"Join meeting\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"inline-flex items-center space-x-2 text-sm text-gray-500 hover:text-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Settings_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Check your audio and video settings\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/meet/PreJoinScreen.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/meet/PreJoinScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./components/meet/VideoConference.tsx":
/*!*********************************************!*\
  !*** ./components/meet/VideoConference.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VideoConference: () => (/* binding */ VideoConference)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-COF-7zxu.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/components-k0KtCs0w.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/prefabs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/room-DhBnHppi.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(ssr)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,MicOff,MoreVertical,Users,Video,VideoOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* __next_internal_client_entry_do_not_use__ VideoConference auto */ \n\n\n\n\nfunction MeetingRoom() {\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isParticipantsOpen, setIsParticipantsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const participants = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_3__.K)();\n    const tracks = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_3__.t)([\n        {\n            source: livekit_client__WEBPACK_IMPORTED_MODULE_2__.Track.Source.Camera,\n            withPlaceholder: true\n        },\n        {\n            source: livekit_client__WEBPACK_IMPORTED_MODULE_2__.Track.Source.ScreenShare,\n            withPlaceholder: false\n        }\n    ], {\n        onlySubscribed: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-900 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 text-white px-4 py-3 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"font-medium\",\n                                children: \"Meeting Room\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            participants.length,\n                                            \" participants\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsParticipantsOpen(!isParticipantsOpen),\n                                className: `p-2 rounded-lg transition-colors ${isParticipantsOpen ? 'bg-blue-600' : 'hover:bg-gray-700'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsChatOpen(!isChatOpen),\n                                className: `p-2 rounded-lg transition-colors ${isChatOpen ? 'bg-blue-600' : 'hover:bg-gray-700'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 hover:bg-gray-700 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_7__.G, {\n                            tracks: tracks,\n                            style: {\n                                height: '100%'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_7__.P, {}, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    (isChatOpen || isParticipantsOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l border-gray-200 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setIsParticipantsOpen(true);\n                                                setIsChatOpen(false);\n                                            },\n                                            className: `pb-2 border-b-2 transition-colors ${isParticipantsOpen ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                                            children: [\n                                                \"Participants (\",\n                                                participants.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setIsChatOpen(true);\n                                                setIsParticipantsOpen(false);\n                                            },\n                                            className: `pb-2 border-b-2 transition-colors ${isChatOpen ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                                            children: \"Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: [\n                                    isParticipantsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 space-y-3\",\n                                        children: participants.map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                        children: participant.name?.[0]?.toUpperCase() || 'A'\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: [\n                                                                    participant.name || 'Anonymous',\n                                                                    participant.isLocal && ' (You)'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                                children: [\n                                                                    participant.isMicrophoneEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    participant.isCameraEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                        lineNumber: 149,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_MicOff_MoreVertical_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                        lineNumber: 151,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, participant.identity, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this),\n                                    isChatOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.Chat, {\n                                            style: {\n                                                height: '100%'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.ControlBar, {\n                        variation: \"minimal\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_7__.R, {}, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\nfunction VideoConference({ roomName, token, userName }) {\n    const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_WS_URL || 'ws://localhost:7880';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_13__.L, {\n        video: true,\n        audio: true,\n        token: token,\n        serverUrl: serverUrl,\n        \"data-lk-theme\": \"default\",\n        style: {\n            height: '100vh'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MeetingRoom, {}, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/meet/VideoConference.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/meet/VideoConference.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRStCO0FBQ21DO0FBQzNELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQVk7SUFDdkQscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBhbnkpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+O1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\nconst Toaster = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMkM7QUFJM0MsTUFBTUEsVUFBVSxDQUFDLEVBQUUsR0FBR0UsT0FBcUI7SUFDekMscUJBQ0UsOERBQUNELDJDQUFNQTtRQUNMRSxXQUFVO1FBQ1ZDLGNBQWM7WUFDWkMsWUFBWTtnQkFDVkMsT0FDRTtnQkFDRkMsYUFBYTtnQkFDYkMsY0FDRTtnQkFDRkMsY0FDRTtZQUNKO1FBQ0Y7UUFDQyxHQUFHUCxLQUFLOzs7Ozs7QUFHZjtBQUVtQiIsInNvdXJjZXMiOlsiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFRvYXN0ZXIgYXMgU29ubmVyIH0gZnJvbSBcInNvbm5lclwiO1xuXG50eXBlIFRvYXN0ZXJQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBTb25uZXI+O1xuXG5jb25zdCBUb2FzdGVyID0gKHsgLi4ucHJvcHMgfTogVG9hc3RlclByb3BzKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFNvbm5lclxuICAgICAgY2xhc3NOYW1lPVwidG9hc3RlciBncm91cFwiXG4gICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgY2xhc3NOYW1lczoge1xuICAgICAgICAgIHRvYXN0OlxuICAgICAgICAgICAgXCJncm91cCB0b2FzdCBncm91cC1bLnRvYXN0ZXJdOmJnLWJhY2tncm91bmQgZ3JvdXAtWy50b2FzdGVyXTp0ZXh0LWZvcmVncm91bmQgZ3JvdXAtWy50b2FzdGVyXTpib3JkZXItYm9yZGVyIGdyb3VwLVsudG9hc3Rlcl06c2hhZG93LWxnXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiZ3JvdXAtWy50b2FzdF06dGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgYWN0aW9uQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1wcmltYXJ5IGdyb3VwLVsudG9hc3RdOnRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgY2FuY2VsQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1tdXRlZCBncm91cC1bLnRvYXN0XTp0ZXh0LW11dGVkLWZvcmVncm91bmRcIixcbiAgICAgICAgfSxcbiAgICAgIH19XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn07XG5cbmV4cG9ydCB7IFRvYXN0ZXIgfTsiXSwibmFtZXMiOlsiVG9hc3RlciIsIlNvbm5lciIsInByb3BzIiwiY2xhc3NOYW1lIiwidG9hc3RPcHRpb25zIiwiY2xhc3NOYW1lcyIsInRvYXN0IiwiZGVzY3JpcHRpb24iLCJhY3Rpb25CdXR0b24iLCJjYW5jZWxCdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./convex/_generated/api.js":
/*!**********************************!*\
  !*** ./convex/_generated/api.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   internal: () => (/* binding */ internal)\n/* harmony export */ });\n/* harmony import */ var convex_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! convex/server */ \"(ssr)/./node_modules/convex/dist/esm/server/index.js\");\n/* eslint-disable */ /**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */ \n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */ const api = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\nconst internal = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb252ZXgvX2dlbmVyYXRlZC9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsa0JBQWtCLEdBQ2xCOzs7Ozs7O0NBT0MsR0FFc0M7QUFFdkM7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1DLE1BQU1ELGlEQUFNQSxDQUFDO0FBQ25CLE1BQU1FLFdBQVdGLGlEQUFNQSxDQUFDIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbnZleC9fZ2VuZXJhdGVkL2FwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSAqL1xuLyoqXG4gKiBHZW5lcmF0ZWQgYGFwaWAgdXRpbGl0eS5cbiAqXG4gKiBUSElTIENPREUgSVMgQVVUT01BVElDQUxMWSBHRU5FUkFURUQuXG4gKlxuICogVG8gcmVnZW5lcmF0ZSwgcnVuIGBucHggY29udmV4IGRldmAuXG4gKiBAbW9kdWxlXG4gKi9cblxuaW1wb3J0IHsgYW55QXBpIH0gZnJvbSBcImNvbnZleC9zZXJ2ZXJcIjtcblxuLyoqXG4gKiBBIHV0aWxpdHkgZm9yIHJlZmVyZW5jaW5nIENvbnZleCBmdW5jdGlvbnMgaW4geW91ciBhcHAncyBBUEkuXG4gKlxuICogVXNhZ2U6XG4gKiBgYGBqc1xuICogY29uc3QgbXlGdW5jdGlvblJlZmVyZW5jZSA9IGFwaS5teU1vZHVsZS5teUZ1bmN0aW9uO1xuICogYGBgXG4gKi9cbmV4cG9ydCBjb25zdCBhcGkgPSBhbnlBcGk7XG5leHBvcnQgY29uc3QgaW50ZXJuYWwgPSBhbnlBcGk7XG4iXSwibmFtZXMiOlsiYW55QXBpIiwiYXBpIiwiaW50ZXJuYWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./convex/_generated/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/convex-provider.tsx */ \"(ssr)/./components/convex-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/livepeer-provider.tsx */ \"(ssr)/./components/livepeer-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/rooms/[roomName]/page.tsx */ \"(ssr)/./app/rooms/[roomName]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZhcHAlMkZyb29tcyUyRiU1QnJvb21OYW1lJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBLQUErRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9hcHAvcm9vbXMvW3Jvb21OYW1lXS9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Frooms%2F%5BroomName%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/convex","vendor-chunks/swr","vendor-chunks/sonner","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/next-themes","vendor-chunks/snakecase-keys","vendor-chunks/use-sync-external-store","vendor-chunks/no-case","vendor-chunks/dequal","vendor-chunks/lower-case","vendor-chunks/@swc","vendor-chunks/jwt-decode","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/lucide-react","vendor-chunks/@livekit","vendor-chunks/livekit-client"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Frooms%2F%5BroomName%5D%2Fpage&page=%2Frooms%2F%5BroomName%5D%2Fpage&appPaths=%2Frooms%2F%5BroomName%5D%2Fpage&pagePath=private-next-app-dir%2Frooms%2F%5BroomName%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();